global class ProcessWorkitemRequest {
	global String action;
	global String workitemid;
	global ProcessWorkitemRequest() { }
	global Boolean equals(Object obj) { }
	global String getAction() { }
	global String getComments() { }
	global List<Id> getNextApproverIds() { }
	global String getWorkitemId() { }
	global Integer hashCode() { }
	global void setAction(String param0) { }
	global void setComments(String param0) { }
	global void setNextApproverIds(List<Id> param0) { }
	global void setWorkitemId(String param0) { }
	global String toString() { }

}