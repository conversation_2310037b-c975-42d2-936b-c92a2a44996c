global class VoucherRepresentation {
	global String currencyIsoCode;
	global String description;
	global Integer discountPercent;
	global Datetime effectiveDate;
	global Datetime effectiveDateTime;
	global Datetime expirationDate;
	global Datetime expirationDateTime;
	global Double faceValue;
	global Boolean hasTimeBasedVoucherPeriod;
	global Boolean isVoucherDefinitionActive;
	global Boolean isVoucherPartiallyRedeemable;
	global String partnerAccountName;
	global String productCategoryId;
	global String productCategoryName;
	global String productId;
	global String productName;
	global String promotionId;
	global String promotionName;
	global Double redeemedValue;
	global Double remainingValue;
	global String status;
	global String type;
	global Datetime useDate;
	global String voucherCode;
	global String voucherDefinition;
	global String voucherId;
	global String voucherImageUrl;
	global String voucherNumber;
	global VoucherRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}