global class WorkspacePermission {
	global Boolean canAddFileComments;
	global Boolean canAddFiles;
	global Boolean canAddFilesOnBehalfOfOthers;
	global Boolean canArchiveFiles;
	global Boolean canAttachOrShareFilesToFeed;
	global Boolean canDeleteFiles;
	global Boolean canDeliverContent;
	global Boolean canFeatureFiles;
	global Boolean canManageWorkspace;
	global Boolean canModifyFileComments;
	global Boolean canOrganizeFilesAndFolders;
	global Boolean canTagFiles;
	global Boolean canViewFileComments;
	global WorkspacePermission() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}