global class WishlistItem {
	global String currencyIsoCode;
	global ConnectApi.ErrorResponse error;
	global Double listPrice;
	global ConnectApi.CartItemProduct productSummary;
	global Double salesPrice;
	global String wishlistItemId;
	global WishlistItem() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}