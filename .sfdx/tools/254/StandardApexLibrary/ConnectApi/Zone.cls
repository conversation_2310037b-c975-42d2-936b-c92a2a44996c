global class Zone {
	global String description;
	global String id;
	global Boolean isActive;
	global Boolean isChatterAnswers;
	global String name;
	global String url;
	global ConnectApi.ZoneShowIn visibility;
	global String visibilityId;
	global Zone() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}