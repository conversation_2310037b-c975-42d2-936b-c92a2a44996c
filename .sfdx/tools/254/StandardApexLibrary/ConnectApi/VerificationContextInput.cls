global class VerificationContextInput {
	global ConnectApi.ProcessDefinitionInput processDefinition;
	global ConnectApi.SelectedSearchResultInput selectedSearchResult;
	global ConnectApi.SelectedVerifiedResultInput selectedVerifiedResult;
	global ConnectApi.VerifiedResultInput verifiedResult;
	global VerificationContextInput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Integer hashCode() { }
	global String toString() { }

}