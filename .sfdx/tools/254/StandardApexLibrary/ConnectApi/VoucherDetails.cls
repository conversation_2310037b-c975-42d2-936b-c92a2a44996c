global class VoucherDetails {
	global Datetime effectiveDate;
	global Datetime expirationDate;
	global String transactionJournalId;
	global String voucherCode;
	global String voucherDefinition;
	global String voucherId;
	global String voucherImageUrl;
	global VoucherDetails() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}