global class UnifiedKnowledgeConnector {
	global List<String> customFields;
	global String developerName;
	global String fieldMapping;
	global String id;
	global List<String> includedElements;
	global List<String> includedLanguages;
	global List<String> includedSources;
	global Boolean interArticleLinksEnabled;
	global String label;
	global Datetime lastRunDate;
	global Boolean openInSource;
	global String queryFilterCondition;
	global String recordType;
	global String rtaField;
	global ConnectApi.UnifiedKnowledgeS3UploadCredentials s3UploadCredentials;
	global String source;
	global String status;
	global String table;
	global String taxonomyMapping;
	global Integer totalArticles;
	global Boolean visibleInCsp;
	global Boolean visibleInPkb;
	global Boolean visibleInPrm;
	global UnifiedKnowledgeConnector() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}