global class VerifiedDetailsOutputRepresentation {
	global String displayRecordId;
	global String displayRecordName;
	global String label;
	global String processDetailName;
	global String searchObjectName;
	global String verifiedId;
	global VerifiedDetailsOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}