global class VoucherWithPriceOutput {
	global Double discountPercentage;
	global Integer expirationDuration;
	global Long issueTime;
	global Long nonce;
	global Double originalPricePerToken;
	global Double pricePerToken;
	global String signature;
	global String walletId;
	global Long weiPerToken;
	global VoucherWithPriceOutput() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}