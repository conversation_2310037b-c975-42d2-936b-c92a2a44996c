global class User {
	global String additionalLabel;
	global String communityNickname;
	global String companyName;
	global String displayName;
	global String firstName;
	global Boolean isInThisCommunity;
	global String lastName;
	global ConnectApi.OutOfOffice outOfOffice;
	global ConnectApi.Photo photo;
	global ConnectApi.Reputation reputation;
	global List<ConnectApi.Stamp> stamps;
	global String title;
	global ConnectApi.UserType userType;
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}