global class VerificationProcessFieldOutputRepresentation {
	global String dataSourceType;
	global String dataType;
	global String developerName;
	global String fieldName;
	global String fieldType;
	global String fieldValueFormula;
	global Boolean isManualInput;
	global String label;
	global VerificationProcessFieldOutputRepresentation() { }
	global Object clone() { }
	global Boolean equals(Object obj) { }
	global Double getBuildVersion() { }
	global Integer hashCode() { }
	global String toString() { }

}