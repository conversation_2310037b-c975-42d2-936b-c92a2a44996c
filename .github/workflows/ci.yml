name: CI Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Lint code
        run: npm run lint

      - name: Type check
        run: npm run type-check

      - name: Build application
        run: npm run build

      - name: Run unit tests
        run: npm test -- --coverage

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Start application
        run: npm run start &

      - name: Run E2E tests
        run: npx playwright test --config=playwright.config.ts --reporter=list

  build-electron:
    needs: test
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            build-command: npm run electron:linux
          - os: windows-latest
            build-command: npm run electron:win
          - os: macos-latest
            build-command: npm run electron:mac
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build Next.js application
        run: npm run build

      - name: Build Electron application
        run: ${{ matrix.build-command }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: electron-app-${{ matrix.os }}
          path: |
            dist/*.exe
            dist/*.dmg
            dist/*.AppImage
            dist/*.deb
            dist/*.rpm
          if-no-files-found: ignore
